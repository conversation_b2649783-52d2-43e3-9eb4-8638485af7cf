#!/usr/bin/env python3
"""
Import LinkedIn coaching insights from Notion API to MongoDB Atlas
"""

import os
import time
from datetime import datetime, timezone
from typing import Dict, Optional
from pymongo import MongoClient, IndexModel, TEXT
from pymongo.errors import ConnectionFailure
from dotenv import load_dotenv
from notion_client import Client

# Load environment variables
load_dotenv()

def utcnow():
    """Get current UTC time"""
    return datetime.now(timezone.utc)

def with_retries(fn, retries=3):
    """Execute function with exponential backoff retry logic"""
    for i in range(retries):
        try:
            return fn()
        except Exception as e:
            if i == retries - 1:
                raise
            # Check for rate limiting
            if 'rate_limited' in str(e).lower() or '429' in str(e):
                time.sleep(2 ** i)
            else:
                time.sleep(1)  # Brief pause for other errors

class CoachingDataImporter:
    def __init__(self, connection_string: str = None):
        """Initialize MongoDB and Notion connections with proper timeouts and retry logic"""
        self.connection_string = connection_string or os.getenv('MONGODB_URI')
        if not self.connection_string:
            raise ValueError("MongoDB connection string not provided. Set MONGODB_URI in .env file")
        
        try:
            # MongoDB with proper timeouts and retry settings
            self.client = MongoClient(
                self.connection_string,
                serverSelectionTimeoutMS=5000,
                connectTimeoutMS=5000,
                retryWrites=True,
                retryReads=True
            )
            # Test connection
            self.client.admin.command('ping')
            print("✅ Connected to MongoDB Atlas")
            
            self.db = self.client.coaching_insights
            self.collection = self.db.linkedin_posts
            self.sync_state_collection = self.db.sync_state
            
        except ConnectionFailure as e:
            print(f"❌ Failed to connect to MongoDB: {e}")
            raise
        
        # Initialize Notion client
        self.notion_token = os.getenv('NOTION_TOKEN')
        self.notion_database_id = os.getenv('NOTION_DATABASE_ID')
        
        if not self.notion_token or not self.notion_database_id:
            raise ValueError("Notion credentials not provided. Set NOTION_TOKEN and NOTION_DATABASE_ID in .env file")
        
        self.notion_client = Client(
            auth=self.notion_token,
            # Add user agent for traceability
            notion_version="2022-06-28"
        )
        print("✅ Connected to Notion API")
    
    def query_notion_with_retry(self, **kwargs):
        """Query Notion API with retry logic for rate limiting"""
        def _query():
            return self.notion_client.databases.query(**kwargs)
        
        return with_retries(_query, retries=5)
    
    def parse_notion_page(self, page: Dict) -> Dict:
        """Parse Notion API page into document structure with proper type handling"""
        doc = {
            'notion_page_id': page['id'],
            'source': 'notion_api',
            # imported_at handled via $setOnInsert in upsert
        }
        
        # Extract properties from Notion page
        props = page.get('properties', {})
        
        # Map Notion properties to our schema
        property_mapping = {
            'Title': 'title',
            'Client': 'client', 
            'Your Coaching': 'coaching_feedback',
            'Original Content': 'original_content',
            'Edited Content': 'edited_content',
            'Post Type': 'post_type',
            'Content Strategy Tags': 'content_strategy_tags',
            'Key Principles': 'key_principles',
            'Product Potential': 'product_potential',
            'Date': 'date'
        }
        
        for notion_prop, doc_field in property_mapping.items():
            if notion_prop not in props:
                continue
                
            try:
                prop_data = props[notion_prop]
                prop_type = prop_data.get('type')
                
                # Extract value based on Notion property type
                if prop_type == 'title':
                    texts = [t.get('plain_text', '') for t in prop_data.get('title', [])]
                    doc[doc_field] = ' '.join(texts) if texts else None
                elif prop_type == 'rich_text':
                    texts = [t.get('plain_text', '') for t in prop_data.get('rich_text', [])]
                    doc[doc_field] = ' '.join(texts) if texts else None
                elif prop_type == 'select':
                    select_data = prop_data.get('select')
                    doc[doc_field] = select_data.get('name') if select_data else None
                elif prop_type == 'multi_select':
                    tags = [tag.get('name', '') for tag in prop_data.get('multi_select', [])]
                    doc[doc_field] = tags if tags else []
                elif prop_type == 'date':
                    date_data = prop_data.get('date')
                    if date_data and date_data.get('start'):
                        try:
                            # Normalize to UTC
                            dt = datetime.fromisoformat(
                                date_data['start'].replace('Z', '+00:00')
                            ).astimezone(timezone.utc)
                            doc[doc_field] = dt
                        except Exception:
                            # Skip if date parsing fails
                            pass
            except Exception as e:
                # Log but don't fail on single property parsing errors
                print(f"  ⚠️ Warning: Failed to parse property {notion_prop}: {e}")
        
        return doc
    
    def upsert_document(self, doc: Dict) -> str:
        """Upsert a single document with proper timestamp handling"""
        now = utcnow()
        
        # Remove imported_at from doc if present (will use $setOnInsert)
        doc.pop('imported_at', None)
        
        result = self.collection.update_one(
            {'notion_page_id': doc['notion_page_id']},
            {
                '$set': {**doc, 'last_updated': now},
                '$setOnInsert': {'imported_at': now}
            },
            upsert=True
        )
        
        if result.upserted_id:
            return 'inserted'
        elif result.modified_count > 0:
            return 'updated'
        else:
            return 'unchanged'
    
    def save_sync_checkpoint(self, checkpoint_time: datetime, cursor: Optional[str] = None):
        """Save sync checkpoint for recovery from partial failures"""
        self.sync_state_collection.update_one(
            {'_id': 'notion_sync'},
            {
                '$set': {
                    'last_checkpoint': checkpoint_time,
                    'last_cursor': cursor,
                    'updated_at': utcnow()
                }
            },
            upsert=True
        )
    
    def get_sync_checkpoint(self) -> tuple[Optional[datetime], Optional[str]]:
        """Get last sync checkpoint"""
        state = self.sync_state_collection.find_one({'_id': 'notion_sync'})
        if state:
            return state.get('last_checkpoint'), state.get('last_cursor')
        return None, None
    
    def sync_from_notion(self, use_checkpoint: bool = True) -> Dict[str, int]:
        """Sync documents from Notion API with proper error handling and checkpointing"""
        print("🔄 Syncing from Notion API...")
        
        # Get checkpoint if using incremental sync
        checkpoint_time, last_cursor = (None, None)
        if use_checkpoint:
            checkpoint_time, last_cursor = self.get_sync_checkpoint()
            if checkpoint_time:
                print(f"📅 Resuming from checkpoint: {checkpoint_time}")
        
        # Build filter for incremental sync based on last edited time
        filter_params = {}
        if checkpoint_time:
            filter_params = {
                'filter': {
                    'timestamp': 'last_edited_time',
                    'last_edited_time': {
                        'after': checkpoint_time.isoformat()
                    }
                }
            }
        
        stats = {'inserted': 0, 'updated': 0, 'unchanged': 0, 'errors': 0}
        has_more = True
        start_cursor = last_cursor  # Resume from last cursor if available
        max_edited_time = checkpoint_time or datetime.min.replace(tzinfo=timezone.utc)
        
        while has_more:
            try:
                # Query Notion database with retry logic
                response = self.query_notion_with_retry(
                    database_id=self.notion_database_id,
                    start_cursor=start_cursor,
                    page_size=100,
                    **filter_params
                )
                
                # Process each page
                for page in response.get('results', []):
                    try:
                        # Track max edited time for checkpointing
                        page_edited = page.get('last_edited_time', '')
                        if page_edited:
                            try:
                                edited_dt = datetime.fromisoformat(
                                    page_edited.replace('Z', '+00:00')
                                ).astimezone(timezone.utc)
                                max_edited_time = max(max_edited_time, edited_dt)
                            except:
                                pass
                        
                        doc = self.parse_notion_page(page)
                        result = self.upsert_document(doc)
                        stats[result] += 1
                        
                        # Don't log titles in production (client data privacy)
                        symbol = '✓' if result == 'inserted' else '↻' if result == 'updated' else '='
                        print(f"  {symbol} Document: {result}")
                        
                    except Exception as e:
                        print(f"  ✗ Error processing page: {e}")
                        stats['errors'] += 1
                        continue  # Continue processing other pages
                
                # Check for more pages
                has_more = response.get('has_more', False)
                start_cursor = response.get('next_cursor')
                
                # Save checkpoint after each successful page batch
                if use_checkpoint:
                    self.save_sync_checkpoint(max_edited_time, start_cursor)
                
            except Exception as e:
                print(f"❌ Error querying Notion: {e}")
                # Don't break - let retry logic handle it
                if 'rate' not in str(e).lower():
                    break  # Break on non-rate-limit errors
        
        # Clear cursor on successful completion
        if use_checkpoint and not has_more:
            self.save_sync_checkpoint(max_edited_time, None)
        
        print(f"\n✅ Notion sync complete:")
        print(f"  {stats['inserted']} new, {stats['updated']} updated, {stats['unchanged']} unchanged, {stats['errors']} errors")
        
        return stats
    
    def create_indexes(self):
        """Create named indexes for efficient querying (idempotent)"""
        try:
            indexes = [
                IndexModel([('notion_page_id', 1)], name='u_notion_page_id', unique=True),
                IndexModel(
                    [('title', TEXT), ('original_content', TEXT), 
                     ('edited_content', TEXT), ('coaching_feedback', TEXT)],
                    name='t_content_text'
                ),
                IndexModel([('client', 1)], name='i_client'),
                IndexModel([('post_type', 1)], name='i_post_type'),
                IndexModel([('date', 1)], name='i_date'),
                IndexModel([('content_strategy_tags', 1)], name='i_tags'),
                IndexModel([('last_updated', 1)], name='i_last_updated'),
            ]
            
            # Create indexes (safe if they already exist)
            self.collection.create_indexes(indexes)
            print("✅ Database indexes verified/created")
        except Exception as e:
            print(f"⚠️ Warning creating indexes: {e}")
    
    def get_stats(self):
        """Get statistics about imported data (optimized for small datasets)"""
        total_docs = self.collection.count_documents({})
        notion_docs = self.collection.count_documents({'source': 'notion_api'})
        
        print("\n📊 Database Statistics:")
        print(f"  Total documents: {total_docs}")
        print(f"  From Notion API: {notion_docs}")
        
        # Only run distinct queries on small datasets
        if total_docs < 10000:
            clients = self.collection.distinct('client')
            post_types = self.collection.distinct('post_type')
            print(f"  Unique clients: {len(clients)}")
            print(f"  Post types: {len(post_types)}")


def main():
    """Main sync function"""
    print("🚀 Starting LinkedIn Coaching Data Sync")
    
    try:
        # Initialize importer
        importer = CoachingDataImporter()
        
        # Create indexes if needed (idempotent)
        importer.create_indexes()
        
        # Sync from Notion (will use checkpoint if available)
        stats = importer.sync_from_notion(use_checkpoint=True)
        
        # Generate embeddings for new/updated documents
        if stats['inserted'] > 0 or stats['updated'] > 0:
            print("\n🔮 Generating embeddings for new/updated documents...")
            from generate_embeddings import EmbeddingsGenerator
            embeddings_gen = EmbeddingsGenerator()
            # Only process documents without embeddings (more efficient)
            embeddings_gen.process_all_documents()
        
        # Show statistics
        importer.get_stats()
        
        print("\n✅ Sync complete!")
        
    except Exception as e:
        print(f"\n❌ Sync failed: {e}")
        raise


if __name__ == '__main__':
    main()