name: Sync Notion Coaching Data

on:
  schedule:
    # Run twice a week: Monday and Thursday at 2 AM UTC
    - cron: '0 2 * * 1,4'
  
  workflow_dispatch:  # Allow manual trigger from GitHub UI
    inputs:
      full_sync:
        description: 'Run full sync (ignore checkpoint)'
        required: false
        default: 'false'
        type: boolean

jobs:
  sync:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
      
      - name: Run Notion sync
        env:
          MONGODB_URI: ${{ secrets.MONGODB_URI }}
          NOTION_TOKEN: ${{ secrets.NOTION_TOKEN }}
          NOTION_DATABASE_ID: ${{ secrets.NOTION_DATABASE_ID }}
          VOYAGE_API_KEY: ${{ secrets.VOYAGE_API_KEY }}
        run: |
          echo "🔄 Starting Notion sync..."
          python import_coaching_data.py
      
      - name: Sync status notification (optional)
        if: failure()
        run: |
          echo "❌ Sync failed - check logs for details"
          # You could add Slack/email notification here
      
      - name: Success notification
        if: success()
        run: |
          echo "✅ Sync completed successfully"