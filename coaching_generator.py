#!/usr/bin/env python3
"""
Coaching feedback generator using Claude API with RAG pipeline
"""

import os
import time
from typing import Dict, List, Generator, Optional
from anthropic import Anthropic
from dotenv import load_dotenv
from search_pipeline import Coaching<PERSON>earchPipeline
from supabase_client import <PERSON><PERSON><PERSON><PERSON>ogger, generate_session_id

load_dotenv()

class CoachingGenerator:
    # Model configuration constants
    MODEL_NAME = "claude-sonnet-4-20250514"
    MAX_TOKENS = 2000
    TEMPERATURE = 0.7
    
    def __init__(self):
        """Initialize Claude API and search pipeline"""
        # Claude API setup
        api_key = os.getenv('ANTHROPIC_API_KEY')
        if not api_key:
            raise ValueError("Anthropic API key not provided. Set ANTHROPIC_API_KEY in .env file")
        
        self.client = Anthropic(api_key=api_key)
        
        # Initialize search pipeline
        self.search_pipeline = CoachingSearchPipeline()
        
        # Initialize Supabase logger
        try:
            self.logger = SupabaseLogger()
            print("✅ Supabase logger initialized")
        except Exception as e:
            print(f"⚠️ Supabase logger not available: {e}")
            self.logger = None
        
        # Load prompt templates
        self.system_prompt = self._load_system_prompt()
        self.user_prompt_template = self._load_user_prompt_template()
        
        print("✅ Coaching generator initialized")
    
    def _load_system_prompt(self) -> str:
        """Load the system prompt from file"""
        prompt_path = os.path.join(os.path.dirname(__file__), 'prompts', 'system_prompt.txt')
        try:
            with open(prompt_path, 'r') as f:
                return f.read()
        except FileNotFoundError:
            raise FileNotFoundError(f"System prompt not found at {prompt_path}")
    
    def _load_user_prompt_template(self) -> str:
        """Load the user prompt template from file"""
        prompt_path = os.path.join(os.path.dirname(__file__), 'prompts', 'user_prompt.txt')
        try:
            with open(prompt_path, 'r') as f:
                return f.read()
        except FileNotFoundError:
            raise FileNotFoundError(f"User prompt template not found at {prompt_path}")
    
    def _format_user_prompt(self, draft_text: str, search_results: List[Dict]) -> str:
        """Format the user prompt with draft and search results"""
        if not search_results:
            raise ValueError("No similar coaching examples found")
        
        # Extract data from search results for prompt formatting
        prompt_data = {
            'draft_post': draft_text
        }
        
        # Add data from each search result
        for i, result in enumerate(search_results[:3], 1):
            prompt_data[f'client_{i}'] = result.get('client', 'Anonymous')
            prompt_data[f'post_type_{i}'] = result.get('post_type', 'General')
            prompt_data[f'similarity_score_{i}'] = result.get('score', 0)
            prompt_data[f'original_content_{i}'] = result.get('original_content', '')
            prompt_data[f'edited_content_{i}'] = result.get('edited_content', '')
            prompt_data[f'coaching_feedback_{i}'] = result.get('coaching_feedback', '')
        
        # Format the template with actual data
        return self.user_prompt_template.format(**prompt_data)
    
    def _build_request_payload(self, user_prompt: str) -> Dict:
        """Build the API request payload"""
        return {
            "model": self.MODEL_NAME,
            "max_tokens": self.MAX_TOKENS,
            "temperature": self.TEMPERATURE,
            "system": self.system_prompt,
            "messages": [{"role": "user", "content": user_prompt}]
        }
    
    def _log_error(self, session_id: str, draft_text: str, request_payload: Dict, 
                   similar_post_ids: List[str], error: Exception, start_time: float) -> None:
        """Log error to Supabase if logger is available"""
        if self.logger:
            try:
                self.logger.log_api_trace(
                    session_id=session_id,
                    user_draft=draft_text,
                    request_payload=request_payload,
                    response_payload={"error": str(error)},
                    similar_posts=similar_post_ids,
                    latency_ms=int((time.time() - start_time) * 1000),
                    model=self.MODEL_NAME
                )
            except Exception as log_error:
                print(f"⚠️ Failed to log error trace: {log_error}")
    
    def generate_feedback(self, draft_text: str, session_id: Optional[str] = None) -> Generator[str, None, None]:
        """
        Generate coaching feedback for a draft using RAG pipeline with trace logging
        
        Args:
            draft_text: The user's LinkedIn draft
            session_id: Optional session ID for tracking
            
        Yields:
            Chunks of coaching feedback as they're generated
        """
        # Generate session ID if not provided
        if not session_id:
            session_id = generate_session_id()
        
        # Step 1: Search for similar coaching examples
        print("🔍 Finding similar coaching examples...")
        search_results = self.search_pipeline.search_similar_coaching(
            draft_text=draft_text,
            limit=3
        )
        
        # Extract MongoDB IDs for logging
        similar_post_ids = [str(result.get('_id', '')) for result in search_results if result.get('_id')]
        
        if not search_results:
            yield "I couldn't find similar coaching examples in my database. Let me provide general feedback based on best practices.\n\n"
            user_prompt = f"Provide coaching feedback for this LinkedIn draft:\n\n{draft_text}"
        else:
            user_prompt = self._format_user_prompt(draft_text, search_results)
        
        # Prepare request payload for logging
        request_payload = self._build_request_payload(user_prompt)
        
        # Track timing and collect response
        start_time = time.time()
        collected_text = []
        
        print("💭 Generating personalized coaching feedback...")
        
        try:
            with self.client.messages.stream(**request_payload) as stream:
                for text in stream.text_stream:
                    collected_text.append(text)
                    yield text
                
                # After streaming completes, log the trace
                latency_ms = int((time.time() - start_time) * 1000)
                full_response = ''.join(collected_text)
                
                # Get token counts from the stream
                final_message = stream.get_final_message()
                input_tokens = getattr(final_message.usage, 'input_tokens', None)
                output_tokens = getattr(final_message.usage, 'output_tokens', None)
                
                # Log to Supabase if available
                if self.logger:
                    try:
                        self.logger.log_api_trace(
                            session_id=session_id,
                            user_draft=draft_text,
                            request_payload=request_payload,
                            response_payload={
                                "content": [{"text": full_response, "type": "text"}],
                                "usage": {"input_tokens": input_tokens, "output_tokens": output_tokens}
                            },
                            similar_posts=similar_post_ids,
                            input_tokens=input_tokens,
                            output_tokens=output_tokens,
                            latency_ms=latency_ms,
                            model="claude-sonnet-4-20250514"
                        )
                        # Store session_id for the frontend to use
                        yield f"\n<!-- session_id: {session_id} -->"
                    except Exception as log_error:
                        print(f"⚠️ Failed to log trace: {log_error}")
                        
        except Exception as e:
            yield f"\n\nError generating feedback: {str(e)}"
            
            # Log error case
            self._log_error(session_id, draft_text, request_payload, similar_post_ids, e, start_time)
    
    def generate_feedback_complete(self, draft_text: str, session_id: Optional[str] = None) -> Dict:
        """
        Generate complete coaching feedback (non-streaming version) with trace logging
        
        Args:
            draft_text: The user's LinkedIn draft
            session_id: Optional session ID for tracking (generates new one if not provided)
            
        Returns:
            Dictionary with feedback, metadata, and session_id
        """
        # Generate session ID if not provided
        if not session_id:
            session_id = generate_session_id()
        
        # Search for similar examples
        search_results = self.search_pipeline.search_similar_coaching(
            draft_text=draft_text,
            limit=3
        )
        
        # Extract MongoDB IDs from search results for logging
        similar_post_ids = [str(result.get('_id', '')) for result in search_results if result.get('_id')]
        
        # Format prompt
        if not search_results:
            user_prompt = f"Provide coaching feedback for this LinkedIn draft:\n\n{draft_text}"
        else:
            user_prompt = self._format_user_prompt(draft_text, search_results)
        
        # Prepare request payload for logging
        request_payload = self._build_request_payload(user_prompt)
        
        # Track timing
        start_time = time.time()
        
        # Call Claude API (non-streaming)
        try:
            message = self.client.messages.create(**request_payload)
            
            # Calculate latency
            latency_ms = int((time.time() - start_time) * 1000)
            
            # Extract response data
            feedback_text = message.content[0].text
            input_tokens = getattr(message.usage, 'input_tokens', None)
            output_tokens = getattr(message.usage, 'output_tokens', None)
            
            # Prepare response payload for logging
            response_payload = {
                "content": [{"text": feedback_text, "type": "text"}],
                "usage": {
                    "input_tokens": input_tokens,
                    "output_tokens": output_tokens
                }
            }
            
            # Log to Supabase if available
            if self.logger:
                try:
                    self.logger.log_api_trace(
                        session_id=session_id,
                        user_draft=draft_text,
                        request_payload=request_payload,
                        response_payload=response_payload,
                        similar_posts=similar_post_ids,
                        input_tokens=input_tokens,
                        output_tokens=output_tokens,
                        latency_ms=latency_ms,
                        model="claude-sonnet-4-20250514"
                    )
                except Exception as log_error:
                    print(f"⚠️ Failed to log trace: {log_error}")
            
            return {
                'feedback': feedback_text,
                'similar_examples': search_results,
                'model': 'claude-sonnet-4-20250514',
                'session_id': session_id,
                'tokens': {
                    'input': input_tokens,
                    'output': output_tokens
                }
            }
        except Exception as e:
            # Log error case too
            self._log_error(session_id, draft_text, request_payload, similar_post_ids, e, start_time)
            
            return {
                'feedback': f"Error generating feedback: {str(e)}",
                'similar_examples': search_results,
                'session_id': session_id,
                'error': True
            }

def main():
    """Test the coaching generator"""
    generator = CoachingGenerator()
    
    # Test draft
    test_draft = """
    I've been thinking about retirement planning lately.
    
    Most people don't save enough for retirement.
    It's important to start early and be consistent.
    
    What's your retirement strategy?
    """
    
    print("\n📝 TEST DRAFT:")
    print("-" * 40)
    print(test_draft)
    print("-" * 40)
    print("\n💭 GENERATING COACHING FEEDBACK:")
    print("=" * 40)
    
    # Stream the feedback
    feedback_parts = []
    for chunk in generator.generate_feedback(test_draft):
        print(chunk, end='', flush=True)
        feedback_parts.append(chunk)
    
    print("\n" + "=" * 40)
    print("\n✅ Coaching feedback generated successfully!")

if __name__ == '__main__':
    main()