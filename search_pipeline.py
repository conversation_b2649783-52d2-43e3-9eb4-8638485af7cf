#!/usr/bin/env python3
"""
Search pipeline for finding similar LinkedIn coaching examples
"""

import os
from typing import List, Dict, Optional
from pymongo import MongoClient
from dotenv import load_dotenv
import voyageai

load_dotenv()

class CoachingSearchPipeline:
    def __init__(self):
        """Initialize MongoDB and Voyage AI connections"""
        # MongoDB setup
        self.connection_string = os.getenv('MONGODB_URI')
        if not self.connection_string:
            raise ValueError("MongoDB connection string not provided. Set MONGODB_URI in .env file")
        
        self.client = MongoClient(self.connection_string)
        self.db = self.client.coaching_insights
        self.collection = self.db.linkedin_posts
        
        # Voyage AI setup
        voyage_api_key = os.getenv('VOYAGE_API_KEY')
        if not voyage_api_key:
            raise ValueError("Voyage AI API key not provided. Set VOYAGE_API_KEY in .env file")
        
        self.voyage_client = voyageai.Client(api_key=voyage_api_key)
        self.model = "voyage-3-large"
        
        print(f"✅ Search pipeline initialized (using {self.model})")
    
    def embed_user_draft(self, draft_text: str) -> Optional[List[float]]:
        """Generate embedding for user's draft post"""
        if not draft_text or not draft_text.strip():
            return None
        
        try:
            result = self.voyage_client.embed(
                texts=[draft_text],
                model=self.model,
                input_type="query"  # Use 'query' for search queries
            )
            return result.embeddings[0]
        except Exception as e:
            print(f"❌ Error generating embedding: {e}")
            return None
    
    def search_similar_coaching(self, 
                               draft_text: str, 
                               search_field: str = "original_content_embedding",
                               limit: int = 3) -> List[Dict]:
        """
        Search for similar coaching examples based on user's draft
        
        Args:
            draft_text: The user's draft LinkedIn post
            search_field: Which embedding field to search against
                        Options: "original_content_embedding", 
                                "edited_content_embedding", 
                                "coaching_feedback_embedding"
            limit: Number of results to return
        
        Returns:
            List of similar coaching examples with scores
        """
        # Generate embedding for user's draft
        print("🔄 Generating embedding for your draft...")
        draft_embedding = self.embed_user_draft(draft_text)
        
        if not draft_embedding:
            print("❌ Could not generate embedding for draft")
            return []
        
        # Perform vector search
        print(f"🔍 Searching for {limit} most similar coaching examples...")
        
        pipeline = [
            {
                "$vectorSearch": {
                    "index": "vector_index",  # Your index name
                    "path": f"embeddings.{search_field}",
                    "queryVector": draft_embedding,
                    "numCandidates": 20,  # Cast wider net
                    "limit": limit
                }
            },
            {
                "$project": {
                    "title": 1,
                    "client": 1,
                    "post_type": 1,
                    "original_content": 1,
                    "edited_content": 1,
                    "coaching_feedback": 1,
                    "content_strategy_tags": 1,
                    "score": {"$meta": "vectorSearchScore"}
                }
            }
        ]
        
        try:
            results = list(self.collection.aggregate(pipeline))
            print(f"✅ Found {len(results)} similar examples")
            return results
        except Exception as e:
            print(f"❌ Search error: {e}")
            return []
    
    def format_search_results(self, results: List[Dict]) -> str:
        """Format search results for display"""
        if not results:
            return "No similar coaching examples found."
        
        output = []
        output.append("=" * 80)
        output.append("SIMILAR COACHING EXAMPLES")
        output.append("=" * 80)
        
        for i, result in enumerate(results, 1):
            output.append(f"\n🎯 MATCH #{i} (Similarity: {result.get('score', 0):.3f})")
            output.append("-" * 40)
            
            # Metadata
            if result.get('client'):
                output.append(f"Client: {result['client']}")
            if result.get('post_type'):
                output.append(f"Post Type: {result['post_type']}")
            if result.get('content_strategy_tags'):
                tags = result['content_strategy_tags']
                if isinstance(tags, list):
                    output.append(f"Tags: {', '.join(tags)}")
                else:
                    output.append(f"Tags: {tags}")
            
            output.append("")
            
            # Original content
            output.append("📝 ORIGINAL DRAFT:")
            output.append("-" * 20)
            if result.get('original_content'):
                output.append(result['original_content'][:500])
                if len(result['original_content']) > 500:
                    output.append("... [truncated]")
            else:
                output.append("[No original content available]")
            
            output.append("")
            
            # Edited content
            output.append("✨ EDITED VERSION:")
            output.append("-" * 20)
            if result.get('edited_content'):
                output.append(result['edited_content'][:500])
                if len(result['edited_content']) > 500:
                    output.append("... [truncated]")
            else:
                output.append("[No edited content available]")
            
            output.append("")
            
            # Coaching feedback
            output.append("💡 COACHING FEEDBACK:")
            output.append("-" * 20)
            if result.get('coaching_feedback'):
                output.append(result['coaching_feedback'][:600])
                if len(result['coaching_feedback']) > 600:
                    output.append("... [truncated]")
            else:
                output.append("[No coaching feedback available]")
            
            output.append("")
            output.append("=" * 80)
        
        return "\n".join(output)
    
    def search(self, draft_text: str, limit: int = 3) -> Dict:
        """
        Main search method - returns both raw results and formatted output
        
        Args:
            draft_text: User's draft LinkedIn post
            limit: Number of results to return
        
        Returns:
            Dictionary with 'results' (raw data) and 'formatted' (display string)
        """
        results = self.search_similar_coaching(
            draft_text=draft_text,
            search_field="original_content_embedding",  # Search against original drafts
            limit=limit
        )
        
        return {
            'results': results,
            'formatted': self.format_search_results(results)
        }

def main():
    """Test the search pipeline with a sample draft"""
    # Initialize pipeline
    pipeline = CoachingSearchPipeline()
    
    # Sample draft for testing
    test_draft = """
    I've been thinking about leadership lately.
    
    True leadership isn't about having all the answers.
    It's about asking the right questions and empowering your team to find solutions.
    
    What's your take on leadership?
    """
    
    print("\n📝 TEST DRAFT:")
    print("-" * 40)
    print(test_draft)
    print("-" * 40)
    
    # Perform search
    search_results = pipeline.search(test_draft, limit=3)
    
    # Display results
    print(search_results['formatted'])

if __name__ == '__main__':
    main()