# Product Requirements Document: LinkedIn Coaching Bot PoC

## Executive Summary
Build a proof of concept AI coaching tool that provides <PERSON> Knorr-style feedback on LinkedIn posts by leveraging his existing database of coaching feedback.

## Problem Statement
Adam currently manually reviews 15-20 LinkedIn posts per week, spending ~30 minutes per post writing detailed feedback. This manual process limits his ability to scale his coaching community beyond 6-10 clients.

## Solution Overview
An AI-powered feedback generation tool that accepts LinkedIn drafts and returns Adam-style coaching feedback by finding similar examples from his coaching database.

## Success Criteria for PoC
- [ ] <PERSON> says "This sounds like me" for 7/10 feedback samples
- [ ] Reduces <PERSON>'s review time by 50% (from 30 to 15 minutes)
- [ ] Identifies the same core issues Adam would identify in 8/10 posts
- [ ] Generates feedback within 10 seconds

## Scope - MVP Features

### In Scope
1. Import and process Notion coaching database
2. Build Streamlit interface for draft input → feedback output
3. Implement similarity search and feedback generation

### Out of Scope (for PoC)
- User authentication
- Multiple users/clients
- Feedback history
- Edit/refine feedback
- Client-specific pattern tracking
- Performance analytics
- Mobile optimization
- Batch processing

## Technical Architecture

### Data Pipeline
```
Notion Export → Parse CSV → Create Embeddings (VoyageAI) → Store in MongoDB
```

### Query Flow
```
User Draft → Create Embedding → Similarity Search → Retrieve Examples → Claude Sonnet 4 → Feedback
```

### Tech Stack
- **Database**: MongoDB with vector search
- **LLM**: Claude Sonnet 4
- **Embeddings**: VoyageAI
- **Frontend**: Streamlit
- **Hosting**: Railway

## Development Phases

### Phase 1: Data Prep
- [x] Export Notion database
- [x] Clean and structure data
- [x] Generate embeddings with VoyageAI
- [x] Set up MongoDB Atlas
- [ ] Configure vector search index
- [x] Load documents into MongoDB

### Phase 2: Core API
- [ ] Build embedding and search pipeline
- [ ] Build Claude Sonnet integration

### Phase 3: Streamlit UI
- [ ] Create Streamlit app with text input
- [ ] Connect to API endpoints
- [ ] Display feedback with formatting
- [ ] Add copy-to-clipboard functionality

### Phase 4: Testing with Adam
- [ ] Test with real drafts and iterate
- [ ] Implement in-app feedback system:
  - [ ] Log all inputs/outputs (LLM traces)
  - [ ] Add text field for Adam's custom feedback
  - [ ] Add quality rating rubric (1-5 scale)
  - [ ] Store feedback data for model improvement

## Immediate Next Steps
1. Get Notion database access from Adam
2. Create Anthropic, VoyageAI, and MongoDB Atlas accounts
3. Build data ingestion script with metadata parsing
5. Design classification taxonomy for post types

## Success Looks Like
Adam messages: "Holy shit, I just reviewed 10 posts in 20 minutes instead of 5 hours. When can my clients start using this?"