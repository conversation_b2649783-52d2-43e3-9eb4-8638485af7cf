#!/usr/bin/env python3
"""
Generate embeddings for LinkedIn coaching insights using Voyage AI
"""

import os
import time
from typing import List, Dict, Optional
from pymongo import MongoClient
from dotenv import load_dotenv
import voyageai
from datetime import datetime

load_dotenv()

class EmbeddingsGenerator:
    def __init__(self):
        """Initialize MongoDB and Voyage AI connections"""
        # MongoDB setup
        self.connection_string = os.getenv('MONGODB_URI')
        if not self.connection_string:
            raise ValueError("MongoDB connection string not provided. Set MONGODB_URI in .env file")
        
        self.client = MongoClient(self.connection_string)
        self.db = self.client.coaching_insights
        self.collection = self.db.linkedin_posts
        
        # Voyage AI setup
        voyage_api_key = os.getenv('VOYAGE_API_KEY')
        if not voyage_api_key:
            raise ValueError("Voyage AI API key not provided. Set VOYAGE_API_KEY in .env file")
        
        self.voyage_client = voyageai.Client(api_key=voyage_api_key)
        self.model = "voyage-3-large"
        
        print(f"✅ Connected to MongoDB and Voyage AI (using {self.model})")
    
    def generate_embedding(self, text: str) -> Optional[List[float]]:
        """Generate embedding for a single text"""
        if not text or not text.strip():
            return None
        
        try:
            result = self.voyage_client.embed(
                texts=[text],
                model=self.model,
                input_type="document"
            )
            return result.embeddings[0]
        except Exception as e:
            print(f"❌ Error generating embedding: {e}")
            return None
    
    def generate_embeddings_batch(self, texts: List[str]) -> List[Optional[List[float]]]:
        """Generate embeddings for multiple texts in a batch"""
        # Filter out empty texts but keep track of indices
        valid_indices = []
        valid_texts = []
        for i, text in enumerate(texts):
            if text and text.strip():
                valid_indices.append(i)
                valid_texts.append(text)
        
        if not valid_texts:
            return [None] * len(texts)
        
        try:
            result = self.voyage_client.embed(
                texts=valid_texts,
                model=self.model,
                input_type="document"
            )
            
            # Map results back to original indices
            embeddings = [None] * len(texts)
            for i, idx in enumerate(valid_indices):
                embeddings[idx] = result.embeddings[i]
            
            return embeddings
        except Exception as e:
            print(f"❌ Error generating batch embeddings: {e}")
            return [None] * len(texts)
    
    def update_document_embeddings(self, doc_id, embeddings: Dict[str, List[float]]):
        """Update a document with new embeddings"""
        self.collection.update_one(
            {'_id': doc_id},
            {'$set': {
                'embeddings': embeddings,
                'embeddings_model': self.model,
                'embeddings_generated_at': datetime.now()
            }}
        )
    
    def process_all_documents(self, batch_size: int = 10):
        """Process all documents and generate embeddings"""
        # Count documents needing embeddings
        total_docs = self.collection.count_documents({})
        docs_with_embeddings = self.collection.count_documents({'embeddings': {'$exists': True}})
        
        print(f"\n📊 Found {total_docs} total documents")
        print(f"📊 {docs_with_embeddings} already have embeddings")
        print(f"📊 Processing {total_docs - docs_with_embeddings} documents\n")
        
        # Process documents without embeddings
        cursor = self.collection.find({'embeddings': {'$exists': False}})
        
        processed = 0
        for doc in cursor:
            # Skip documents without a title (likely incomplete)
            if not doc.get('title'):
                print(f"  ⚠️ Skipping document with no title (ID: {doc['_id']})")
                continue
            
            # Prepare texts for embedding
            texts_to_embed = []
            embedding_fields = []
            
            # Check each field and add if it exists
            if doc.get('coaching_feedback'):
                texts_to_embed.append(doc['coaching_feedback'])
                embedding_fields.append('coaching_feedback_embedding')
            else:
                texts_to_embed.append('')
                embedding_fields.append('coaching_feedback_embedding')
            
            if doc.get('original_content'):
                texts_to_embed.append(doc['original_content'])
                embedding_fields.append('original_content_embedding')
            else:
                texts_to_embed.append('')
                embedding_fields.append('original_content_embedding')
            
            if doc.get('edited_content'):
                texts_to_embed.append(doc['edited_content'])
                embedding_fields.append('edited_content_embedding')
            else:
                texts_to_embed.append('')
                embedding_fields.append('edited_content_embedding')
            
            # Generate embeddings
            print(f"Processing: {doc.get('title')[:50]}...")
            embeddings_list = self.generate_embeddings_batch(texts_to_embed)
            
            # Create embeddings dictionary
            embeddings = {}
            for field_name, embedding in zip(embedding_fields, embeddings_list):
                if embedding is not None:
                    embeddings[field_name] = embedding
            
            if embeddings:
                self.update_document_embeddings(doc['_id'], embeddings)
                processed += 1
                print(f"  ✓ Generated {len(embeddings)} embeddings")
            else:
                print(f"  ⚠️ No embeddings generated (empty content)")
            
            # Rate limiting (Voyage AI has rate limits)
            if processed % batch_size == 0:
                time.sleep(1)  # Pause between batches
        
        print(f"\n✅ Processed {processed} documents")
    
    def verify_embeddings(self):
        """Verify embeddings were created correctly"""
        # Check a sample document
        sample = self.collection.find_one({'embeddings': {'$exists': True}})
        
        if sample and 'embeddings' in sample:
            print("\n📋 Sample embeddings structure:")
            embeddings = sample['embeddings']
            for field, embedding in embeddings.items():
                if isinstance(embedding, list):
                    print(f"  - {field}: {len(embedding)} dimensions")
            
            print(f"  - Model: {sample.get('embeddings_model', 'unknown')}")
            print(f"  - Generated: {sample.get('embeddings_generated_at', 'unknown')}")
        
        # Get stats
        total = self.collection.count_documents({})
        with_embeddings = self.collection.count_documents({'embeddings': {'$exists': True}})
        
        print(f"\n📊 Embeddings coverage: {with_embeddings}/{total} documents")

def main():
    """Main function"""
    print("🚀 Starting embeddings generation for LinkedIn coaching insights")
    
    generator = EmbeddingsGenerator()
    generator.process_all_documents()
    generator.verify_embeddings()
    
    print("\n✅ Embeddings generation complete!")

if __name__ == '__main__':
    main()