#!/usr/bin/env python3
"""Test MongoDB Atlas connection"""

import os
from pymongo import MongoClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_connection():
    try:
        # Get connection string
        connection_string = os.getenv('MONGODB_URI')
        if not connection_string:
            print("❌ No MONGODB_URI found in .env file")
            return False
        
        print(f"🔄 Attempting to connect to MongoDB Atlas...")
        print(f"   Connection string: {connection_string[:30]}...")  # Show partial URI for security
        
        # Create client and test connection
        client = MongoClient(connection_string)
        
        # Ping the server to test connection
        client.admin.command('ping')
        
        print("✅ Successfully connected to MongoDB Atlas!")
        
        # List databases
        databases = client.list_database_names()
        print(f"📊 Available databases: {databases}")
        
        # Test creating/accessing our target database
        db = client.coaching_insights
        print(f"✅ Accessed 'coaching_insights' database")
        
        # Check if our collection exists
        collections = db.list_collection_names()
        if collections:
            print(f"📁 Existing collections: {collections}")
        else:
            print("📁 No collections yet (database will be created on first insert)")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

if __name__ == "__main__":
    test_connection()