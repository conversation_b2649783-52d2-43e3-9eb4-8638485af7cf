#!/usr/bin/env python3
"""Test script to verify Supabase trace logging"""

from coaching_generator import CoachingGener<PERSON>

def test_trace_logging():
    """Test that API traces are properly logged to Supabase"""
    
    print("🧪 Testing trace logging to Supabase...")
    
    # Initialize generator
    generator = CoachingGenerator()
    
    # Test draft
    test_draft = """
    Just closed a massive deal for a client's retirement portfolio.
    
    The markets are crazy right now but we found the perfect strategy.
    
    What's your biggest win this week?
    """
    
    print("\n📝 Test draft:")
    print(test_draft)
    print("\n" + "="*50)
    
    # Generate feedback using the complete method (which includes logging)
    print("\n🔄 Generating feedback and logging trace...")
    result = generator.generate_feedback_complete(test_draft)
    
    # Display results
    print("\n✅ Feedback generated!")
    print(f"\n📍 Session ID: {result.get('session_id')}")
    
    if 'tokens' in result:
        print(f"📊 Tokens: {result['tokens'].get('input')} input, {result['tokens'].get('output')} output")
    
    print(f"\n🔍 Similar examples found: {len(result.get('similar_examples', []))}")
    
    print("\n💬 Generated feedback preview:")
    print("-" * 50)
    print(result['feedback'][:500] + "..." if len(result['feedback']) > 500 else result['feedback'])
    
    if generator.logger:
        print("\n✅ Trace should be logged to Supabase!")
        print(f"Check your Supabase dashboard for session: {result.get('session_id')}")
    else:
        print("\n⚠️ Supabase logger not available - trace not logged")

if __name__ == "__main__":
    test_trace_logging()