"""Auth0 authentication configuration for Streamlit app."""
import os
import json
import streamlit as st
from authlib.integrations.requests_client import OAuth2Session
from dotenv import load_dotenv
import jwt
from jwt.exceptions import InvalidTokenError
import time
import logging
import requests
from typing import Optional, Dict, Any

load_dotenv()


class Auth0Config:
    """Configuration for Auth0 authentication."""
    
    def __init__(self):
        self.domain = os.getenv("AUTH0_DOMAIN")
        self.client_id = os.getenv("AUTH0_CLIENT_ID")
        self.client_secret = os.getenv("AUTH0_CLIENT_SECRET")
        self.callback_url = os.getenv("AUTH0_CALLBACK_URL", "http://localhost:8503")
        allowed_domains_env = os.getenv("AUTH0_ALLOWED_DOMAINS", "")
        self.allowed_domains = [d.strip() for d in allowed_domains_env.split(",")] if allowed_domains_env.strip() else []
        
        if not all([self.domain, self.client_id, self.client_secret]):
            raise ValueError(
                "Auth0 configuration incomplete. Please set AUTH0_DOMAIN, "
                "AUTH0_CLIENT_ID, and AUTH0_CLIENT_SECRET environment variables."
            )
        
        self.authorization_base_url = f"https://{self.domain}/authorize"
        self.token_url = f"https://{self.domain}/oauth/token"
        self.userinfo_url = f"https://{self.domain}/userinfo"
        self.logout_url = f"https://{self.domain}/v2/logout"
        self.jwks_url = f"https://{self.domain}/.well-known/jwks.json"


class Auth0Authenticator:
    """Auth0 authentication handler for Streamlit."""
    
    def __init__(self):
        self.config = Auth0Config()
        self._jwks_cache = None
        self._jwks_cache_time = 0
        self._initialize_session_state()
    
    def _initialize_session_state(self):
        """Initialize Auth0 session state variables."""
        auth_keys = ["auth0_token", "auth0_user", "authentication_status", "username", "name"]
        for key in auth_keys:
            if key not in st.session_state:
                st.session_state[key] = None
    
    def _clear_auth_session(self):
        """Clear all authentication-related session state."""
        auth_keys = ["auth0_token", "auth0_user", "authentication_status", "username", "name"]
        for key in auth_keys:
            st.session_state[key] = None
    
    def _get_oauth_session(self, state: Optional[str] = None) -> OAuth2Session:
        """Create OAuth2 session for Auth0."""
        return OAuth2Session(
            client_id=self.config.client_id,
            client_secret=self.config.client_secret,
            redirect_uri=self.config.callback_url,
            scope="openid profile email",
            state=state
        )
    
    def get_authorization_url(self, prompt: str = "consent") -> tuple[str, str]:
        """Get Auth0 authorization URL."""
        oauth = self._get_oauth_session()
        params = {
            "connection": "google-oauth2",  # Force Google login only
            "prompt": prompt  # "consent" to handle first-time users, "login" to force login
        }
        authorization_url, state = oauth.create_authorization_url(
            self.config.authorization_base_url,
            **params
        )
        return authorization_url, state
    
    def handle_callback(self, code: str, state: str) -> bool:
        """Handle OAuth callback from Auth0."""
        try:
            oauth = self._get_oauth_session(state=state)
            
            # Exchange authorization code for token
            token = oauth.fetch_token(
                self.config.token_url,
                code=code,
                client_id=self.config.client_id,
                client_secret=self.config.client_secret
            )
            
            # Store token in session
            st.session_state.auth0_token = token
            
            # Fetch user info
            oauth = OAuth2Session(
                client_id=self.config.client_id,
                token=token
            )
            user_info = oauth.get(self.config.userinfo_url).json()
            
            # Validate email domain if restrictions are configured
            if self.config.allowed_domains:
                email = user_info.get("email", "")
                if not self._validate_email_domain(email):
                    st.error(f"Access denied. Email domain not authorized. Allowed domains: {', '.join(self.config.allowed_domains)}")
                    st.info("Please use an authorized email address to sign in.")
                    # Clear the invalid session
                    self._clear_auth_session()
                    
                    # Show retry button to try different account
                    if st.button("Try Different Account", type="primary"):
                        # Clear query params and session state for fresh login
                        st.query_params.clear()
                        self._clear_auth_session()
                        st.rerun()
                    return False
            
            # Beta whitelist check
            # TODO: Future refactor - Replace with subscription check via Stripe/Paddle webhook
            # that updates Auth0 user metadata or Supabase subscription status
            beta_whitelist_env = os.getenv("BETA_WHITELIST", "")
            if beta_whitelist_env:
                beta_whitelist = [email.strip().lower() for email in beta_whitelist_env.split(",")]
                user_email = user_info.get("email", "").lower()
                
                if user_email not in beta_whitelist:
                    st.error("🔒 Private Beta Access Only")
                    st.info("This app is currently in private beta testing. Please contact us for access.")
                    # Clear the session to prevent access
                    self._clear_auth_session()
                    
                    # Show logout button to try different account
                    if st.button("Try Different Account", type="primary"):
                        # Clear query params and session state for fresh login
                        st.query_params.clear()
                        self._clear_auth_session()
                        st.rerun()
                    return False
            
            # Update session state
            st.session_state.auth0_user = user_info
            st.session_state.authentication_status = True
            # Extract username from email (part before @) for cleaner logging
            email = user_info.get("email")
            if email and "@" in email:
                st.session_state.username = email.split("@")[0]
            else:
                # Fallback to a safe portion of the sub field or full email
                sub = user_info.get("sub", "")
                st.session_state.username = sub.split("|")[-1] if "|" in sub else (email or sub or "user")
            st.session_state.name = user_info.get("name", user_info.get("email"))
            
            return True
            
        except Exception as e:
            st.error(f"Authentication failed: {str(e)}")
            return False
    
    def _validate_email_domain(self, email: str) -> bool:
        """Validate if email belongs to allowed domains."""
        if not self.config.allowed_domains:
            return True  # No restrictions if no domains configured
        
        if not email or "@" not in email:
            return False
        
        domain = email.split("@")[-1].lower()
        allowed_domains_lower = [d.strip().lower() for d in self.config.allowed_domains]
        
        return domain in allowed_domains_lower
    
    def _get_jwks(self) -> Optional[Dict[str, Any]]:
        """Fetch and cache JWKS from Auth0."""
        # Cache JWKS for 1 hour to avoid repeated requests
        if self._jwks_cache and (time.time() - self._jwks_cache_time) < 3600:
            return self._jwks_cache
        
        try:
            response = requests.get(self.config.jwks_url, timeout=10)
            response.raise_for_status()
            self._jwks_cache = response.json()
            self._jwks_cache_time = time.time()
            return self._jwks_cache
        except Exception as e:
            logging.error(f"Failed to fetch JWKS: {e}")
            return None
    
    def _get_signing_key(self, kid: str) -> Optional[str]:
        """Get the signing key for a given key ID."""
        jwks = self._get_jwks()
        if not jwks:
            return None
        
        for key in jwks.get("keys", []):
            if key.get("kid") == kid:
                # Convert JWK to PEM format for PyJWT
                try:
                    from jwt.algorithms import RSAAlgorithm
                    return RSAAlgorithm.from_jwk(key)
                except ImportError:
                    # Fallback if RSAAlgorithm not available
                    logging.warning("RSAAlgorithm not available, falling back to unverified tokens")
                    return None
        
        return None
    
    def validate_token(self) -> bool:
        """Validate the stored token with proper signature verification."""
        if not st.session_state.auth0_token:
            return False
        
        # For ID tokens, check the id_token instead of access_token
        id_token = st.session_state.auth0_token.get("id_token")
        if not id_token:
            # Fall back to access_token if no id_token
            id_token = st.session_state.auth0_token.get("access_token")
            if not id_token:
                return False
        
        try:
            # First decode without verification to get the key ID
            unverified_header = jwt.get_unverified_header(id_token)
            kid = unverified_header.get("kid")
            
            if not kid:
                logging.warning("No key ID found in token header")
                return False
            
            # Get the signing key
            signing_key = self._get_signing_key(kid)
            if not signing_key:
                logging.warning(f"No signing key found for kid: {kid}")
                # Fallback to unverified decode for development
                decoded = jwt.decode(
                    id_token, 
                    options={"verify_signature": False}
                )
            else:
                # Verify token with proper signature verification
                decoded = jwt.decode(
                    id_token,
                    signing_key,
                    algorithms=["RS256"],
                    audience=self.config.client_id,
                    issuer=f"https://{self.config.domain}/"
                )
            
            # Check if token is expired
            exp = decoded.get("exp")
            if exp and exp < time.time():
                return False
            
            # Also check if we have user info
            if not st.session_state.auth0_user:
                return False
            
            return True
            
        except InvalidTokenError as e:
            logging.warning(f"Token validation failed: {e}")
            return False
        except Exception as e:
            # Log unexpected errors but don't expose to user
            logging.error(f"Unexpected error validating token: {e}")
            return False
    
    def _handle_auth_error(self) -> bool:
        """Handle authentication errors from Auth0.
        Returns True if error was handled, False otherwise."""
        if "error" not in st.query_params:
            return False
        
        error = st.query_params.get("error", "")
        error_description = st.query_params.get("error_description", "Authentication failed")
        
        # Handle different error types
        if error == "login_required":
            # No session exists, redirect to login
            st.query_params.clear()
            auth_url, state = self.get_authorization_url(prompt="login")
            st.session_state.auth0_state = state
            st.link_button("Redirecting to login...", auth_url, use_container_width=True)
            st.components.v1.html(f'<script>window.location.href="{auth_url}";</script>', height=0)
        elif error == "access_denied":
            # Domain restriction or other access denial
            st.error("Access denied: Your email domain is not authorized.")
            st.info("Please use your organization email address to sign in.")
            
            if st.button("Sign in with a different account", type="primary"):
                st.query_params.clear()
                auth_url, state = self.get_authorization_url(prompt="select_account")
                st.session_state.auth0_state = state
                st.markdown(f'<meta http-equiv="refresh" content="0;url={auth_url}">', unsafe_allow_html=True)
        else:
            # Other errors
            st.error(f"Login failed: {error_description}")
            
            if st.button("Try Again", type="primary"):
                st.query_params.clear()
                st.rerun()
        
        return True
    
    def _show_login_ui(self):
        """Display the login button interface."""
        _, col2, _ = st.columns([1, 2, 1])
        with col2:
            st.markdown("### LinkedIn Feedback Coach")
            st.markdown("Please sign in to continue")
            
            if st.button("Sign in with Google", type="primary", use_container_width=True):
                auth_url, state = self.get_authorization_url(prompt="login")
                st.session_state.auth0_state = state
                st.markdown(f'<meta http-equiv="refresh" content="0;url={auth_url}">', unsafe_allow_html=True)
    
    def login(self) -> None:
        """Display login interface and handle authentication."""
        # Check for error in query params (Auth0 denial)
        if self._handle_auth_error():
            return
        
        # Check for callback parameters in query params
        if "code" in st.query_params and "state" in st.query_params:
            code = st.query_params["code"]
            state = st.query_params["state"]
            
            if self.handle_callback(code, state):
                # Clear query params after successful auth
                st.query_params.clear()
                st.rerun()
            else:
                # Clear the failed auth attempt params
                st.query_params.clear()
            return
        
        # Check if already authenticated
        if st.session_state.authentication_status and self.validate_token():
            return
        
        # Show login interface
        self._show_login_ui()
    
    def logout(self) -> bool:
        """Handle user logout."""
        if st.sidebar.button("Logout", use_container_width=True):
            # Clear session state
            self._clear_auth_session()
            
            # Redirect to Auth0 logout
            logout_url = (
                f"{self.config.logout_url}?"
                f"client_id={self.config.client_id}&"
                f"returnTo={self.config.callback_url}"
            )
            st.markdown(f'<meta http-equiv="refresh" content="0;url={logout_url}">', unsafe_allow_html=True)
            return True
        return False
    
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """Get current user information."""
        return st.session_state.auth0_user


def get_auth0_authenticator() -> Optional[Auth0Authenticator]:
    """Factory function to get Auth0 authenticator instance."""
    try:
        return Auth0Authenticator()
    except ValueError:
        # Return None if Auth0 is not configured
        # This allows fallback to existing auth method
        return None