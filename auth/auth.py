"""
Auth0 authentication module for the LinkedIn coaching feedback bot
"""
import streamlit as st
from .auth0_config import Auth0Authenticator

def check_authentication():
    """
    Check Auth0 authentication status and handle login/logout.
    Returns tuple of (is_authenticated, name, username)
    """
    
    # Initialize Auth0 authenticator
    try:
        auth0 = Auth0Authenticator()
    except ValueError as e:
        st.error(f"Auth0 configuration error: {e}")
        st.stop()
        return False, None, None
    
    # Handle Auth0 login flow
    auth0.login()
    
    # Check authentication status from session state
    if st.session_state.get("authentication_status"):
        # Get user info from session state
        name = st.session_state.get("name", "")
        username = st.session_state.get("username", "")
        
        # Add user info and logout button in sidebar
        with st.sidebar:
            st.write(f'Welcome *{name}*')
            auth0.logout()
        
        return True, name, username
    
    # If not authenticated, login() will handle the display
    return False, None, None