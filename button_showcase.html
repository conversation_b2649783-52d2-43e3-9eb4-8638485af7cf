<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Style Showcase</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f3f2ef;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 720px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #1a1a1a;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 40px;
            font-size: 0.95rem;
        }
        
        .button-section {
            margin-bottom: 50px;
            padding-bottom: 50px;
            border-bottom: 1px solid #e5e5e5;
        }
        
        .button-section:last-child {
            border-bottom: none;
        }
        
        .button-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c2c2c;
        }
        
        .button-description {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 20px;
        }
        
        /* Base button styles */
        .btn {
            font-size: 1.125rem;
            padding: 1rem 2rem;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
            display: inline-block;
            text-align: center;
            width: 100%;
            max-width: 400px;
        }
        
        /* 1. Gradient with Shadow */
        .btn-gradient {
            background: linear-gradient(135deg, #0A66C2 0%, #004182 100%);
            color: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(10, 102, 194, 0.3);
            font-weight: 500;
            letter-spacing: -0.01em;
        }
        
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(10, 102, 194, 0.4);
        }
        
        /* 2. Ghost Button */
        .btn-ghost {
            background: transparent;
            color: #0A66C2;
            border: 2px solid #0A66C2;
            border-radius: 8px;
            font-weight: 500;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        
        .btn-ghost::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: #0A66C2;
            transition: width 0.3s ease;
            z-index: -1;
        }
        
        .btn-ghost:hover::before {
            width: 100%;
        }
        
        .btn-ghost:hover {
            color: white;
        }
        
        /* 3. Minimal with Icon */
        .btn-minimal {
            background: #0077B5;
            color: white;
            border-radius: 12px;
            font-weight: 400;
            padding: 1rem 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .btn-minimal:hover {
            background: #005885;
        }
        
        .btn-minimal .icon {
            font-size: 1.3rem;
        }
        
        /* 4. Two-tone Split */
        .btn-split {
            background: linear-gradient(90deg, #0A66C2 0%, #0A66C2 85%, #004182 85%, #004182 100%);
            color: white;
            border-radius: 8px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            padding-right: 3rem;
        }
        
        .btn-split::after {
            content: '→';
            position: absolute;
            right: 1.5rem;
            font-size: 1.3rem;
        }
        
        .btn-split:hover {
            background: linear-gradient(90deg, #004182 0%, #004182 85%, #002a54 85%, #002a54 100%);
        }
        
        /* 5. Understated Corporate */
        .btn-corporate {
            background: #4B5563;
            color: white;
            border-radius: 6px;
            font-weight: 400;
            text-transform: uppercase;
            letter-spacing: 0.08em;
            font-size: 0.95rem;
            border-bottom: 3px solid #0A66C2;
            width: auto;
            padding: 0.9rem 3rem;
        }
        
        .btn-corporate:hover {
            background: #374151;
        }
        
        /* 6. Animated Underline */
        .btn-underline {
            background: none;
            color: #0A66C2;
            border: none;
            font-weight: 600;
            font-size: 1.2rem;
            position: relative;
            padding: 0.5rem 0;
            width: auto;
        }
        
        .btn-underline::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: #0A66C2;
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }
        
        .btn-underline:hover::after {
            width: 100%;
        }
        
        /* 7. Current Streamlit Style (for reference) */
        .btn-current {
            background-color: #0A66C2;
            color: white;
            font-size: 1.125rem;
            font-weight: 500;
            padding: 1rem 1.618rem;
            border-radius: 8px;
            letter-spacing: -0.01em;
        }
        
        .btn-current:hover {
            background-color: #004182;
        }
        
        /* 8. Soft Modern */
        .btn-soft {
            background: #EBF5FF;
            color: #0A66C2;
            border-radius: 10px;
            font-weight: 500;
            border: 1px solid rgba(10, 102, 194, 0.2);
            transition: all 0.2s ease;
        }
        
        .btn-soft:hover {
            background: #0A66C2;
            color: white;
            transform: scale(1.02);
        }
        
        /* 9. Bold Statement */
        .btn-bold {
            background: #1a1a1a;
            color: white;
            border-radius: 50px;
            font-weight: 600;
            padding: 1.2rem 2.5rem;
            letter-spacing: -0.02em;
            position: relative;
            overflow: hidden;
        }
        
        .btn-bold::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }
        
        .btn-bold:hover::before {
            width: 300px;
            height: 300px;
        }
        
        /* Center buttons */
        .button-wrapper {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Button Style Options</h1>
        <p class="subtitle">Choose your preferred button style for the LinkedIn Feedback Coach</p>
        
        <!-- Current Style -->
        <div class="button-section">
            <div class="button-title">Current Style (Reference)</div>
            <div class="button-description">What you currently have in the Streamlit app</div>
            <div class="button-wrapper">
                <button class="btn btn-current">Get Coaching Feedback</button>
            </div>
        </div>
        
        <!-- Option 1 -->
        <div class="button-section">
            <div class="button-title">1. Gradient with Shadow</div>
            <div class="button-description">Professional depth with subtle gradient and hover lift</div>
            <div class="button-wrapper">
                <button class="btn btn-gradient">Get Coaching Feedback</button>
            </div>
        </div>
        
        <!-- Option 2 -->
        <div class="button-section">
            <div class="button-title">2. Ghost Button</div>
            <div class="button-description">Elegant outline that fills on hover</div>
            <div class="button-wrapper">
                <button class="btn btn-ghost">Get Coaching Feedback</button>
            </div>
        </div>
        
        <!-- Option 3 -->
        <div class="button-section">
            <div class="button-title">3. Minimal with Icon</div>
            <div class="button-description">Softer blue with coaching icon</div>
            <div class="button-wrapper">
                <button class="btn btn-minimal">
                    Get Coaching Feedback
                    <span class="icon">💬</span>
                </button>
            </div>
        </div>
        
        <!-- Option 4 -->
        <div class="button-section">
            <div class="button-title">4. Two-tone Split</div>
            <div class="button-description">Sophisticated with directional arrow</div>
            <div class="button-wrapper">
                <button class="btn btn-split">Get Coaching Feedback</button>
            </div>
        </div>
        
        <!-- Option 5 -->
        <div class="button-section">
            <div class="button-title">5. Understated Corporate</div>
            <div class="button-description">Professional with accent underline</div>
            <div class="button-wrapper">
                <button class="btn btn-corporate">Get Coaching Feedback</button>
            </div>
        </div>
        
        <!-- Option 6 -->
        <div class="button-section">
            <div class="button-title">6. Animated Underline</div>
            <div class="button-description">Clean editorial style with expanding underline</div>
            <div class="button-wrapper">
                <button class="btn btn-underline">Get Coaching Feedback</button>
            </div>
        </div>
        
        <!-- Option 7 -->
        <div class="button-section">
            <div class="button-title">7. Soft Modern</div>
            <div class="button-description">Light background that inverts on hover</div>
            <div class="button-wrapper">
                <button class="btn btn-soft">Get Coaching Feedback</button>
            </div>
        </div>
        
        <!-- Option 8 -->
        <div class="button-section">
            <div class="button-title">8. Bold Statement</div>
            <div class="button-description">Dark with ripple effect on hover</div>
            <div class="button-wrapper">
                <button class="btn btn-bold">Get Coaching Feedback</button>
            </div>
        </div>
    </div>
</body>
</html>