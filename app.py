import streamlit as st
from coaching_generator import Coach<PERSON><PERSON><PERSON>ator
from supabase_client import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from config import <PERSON>IConfig
from auth import check_authentication

# Initialize the coaching generator (cached to avoid reinitializing)
@st.cache_resource
def get_coaching_generator():
    return CoachingGenerator()

# Initialize Supabase logger (cached)
@st.cache_resource
def get_supabase_logger():
    try:
        return SupabaseLogger()
    except Exception as e:
        st.warning(f"Feedback logging not available: {e}")
        return None

def render_draft_input() -> str:
    """Render draft input area and return text"""
    draft_post = st.text_area(
        "Paste your LinkedIn draft here:",
        height=UIConfig.TEXTAREA_HEIGHT,
        placeholder="Start with a strong hook...\n\nTell your story...\n\nEnd with engagement...",
        help=f"Maximum {UIConfig.MAX_CHAR_LIMIT} characters for optimal LinkedIn performance"
    )
    
    # Character counter
    char_count = len(draft_post)
    st.caption(f"📝 {char_count}/{UIConfig.MAX_CHAR_LIMIT} characters")
    
    return draft_post

def render_header():
    """Display app title and description"""
    st.title("LinkedIn Feedback Coach")
    st.caption("Get actionable coaching on your financial advisor content")
    st.divider()

def setup_page_config():
    """Configure Streamlit page settings"""
    st.set_page_config(
        page_title="LinkedIn Coach",
        page_icon="💼",
        layout="centered",  # Focused, not wide
        initial_sidebar_state="collapsed"  # Hide sidebar
    )

def get_custom_css() -> str:
    """Return custom CSS styles for the app"""
    return f"""
        <style>
        /* Professional Typography */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        * {{
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
        }}
        
        .main > div {{
            padding-top: 2rem;
            max-width: {UIConfig.MAX_WIDTH};
            margin: 0 auto;
        }}
        
        /* Better spacing using golden ratio */
        h1 {{
            font-size: {UIConfig.TITLE_SIZE};
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 0.618rem;
            letter-spacing: -0.02em;
        }}
        
        .stTextArea textarea {{
            font-size: {UIConfig.BODY_SIZE};
            line-height: 1.618;
            letter-spacing: -0.01em;
        }}
        
        .stButton > button {{
            width: 100%;
            background: {UIConfig.BACKGROUND_COLOR};
            color: {UIConfig.PRIMARY_COLOR};
            font-size: {UIConfig.BODY_SIZE};
            font-weight: 500;
            padding: 1rem 1.618rem;
            margin-top: 1.618rem;
            border-radius: 10px;
            border: 1px solid {UIConfig.BORDER_COLOR};
            letter-spacing: -0.01em;
            transition: all 0.2s ease;
        }}
        
        .stButton > button:hover {{
            background: {UIConfig.PRIMARY_COLOR};
            color: white;
            transform: scale(1.02);
            border: 1px solid {UIConfig.PRIMARY_COLOR};
        }}
        
        /* Better caption styling */
        .caption {{
            font-size: {UIConfig.CAPTION_SIZE};
            color: #666;
            line-height: 1.5;
        }}
        </style>
    """

def main():
    # Setup page configuration
    setup_page_config()
    
    # Apply custom CSS styling
    st.markdown(get_custom_css(), unsafe_allow_html=True)
    
    # Check authentication
    is_authenticated, name, username = check_authentication()
    
    if not is_authenticated:
        return
    
    # Render header
    render_header()
    
    # Get draft input from user
    draft_post = render_draft_input()
    
    # Single action button
    if st.button("Get Coaching Feedback", type="primary", use_container_width=True):
        
        if not draft_post:
            st.warning("Please enter your LinkedIn draft first")
        else:
            # Clear previous session state
            for key in ['last_feedback', 'current_session_id', 'show_rating', 'rating_submitted']:
                if key in st.session_state:
                    del st.session_state[key]
            
            # Initialize generator
            generator = get_coaching_generator()
            
            # Clean loading state
            with st.spinner("Finding similar coaching examples..."):
                pass  # The actual search happens during streaming
            
            # Generate real coaching feedback
            def generate_coaching_feedback():
                try:
                    # Use the actual coaching generator
                    for chunk in generator.generate_feedback(draft_post):
                        # Check if this is the session_id marker
                        if chunk.startswith("\n<!-- session_id:") and chunk.endswith("-->"):
                            # Extract and store session_id
                            session_id = chunk.split("session_id: ")[1].split(" -->")[0]
                            st.session_state['current_session_id'] = session_id
                            # Don't yield the marker to the display
                        else:
                            yield chunk
                except Exception as e:
                    yield f"\n\n⚠️ Error generating feedback: {str(e)}"
            
            # Stream the feedback below the button
            with st.container():
                st.divider()
                st.subheader("Your Coaching Feedback")
                feedback_text = st.write_stream(generate_coaching_feedback())
                # Store the generated feedback in session state immediately
                st.session_state['last_feedback'] = feedback_text
                st.session_state['show_rating'] = True
                st.session_state['just_generated'] = True
    
    # Display existing feedback from session state (on subsequent runs)
    if 'last_feedback' in st.session_state and not st.session_state.get('just_generated', False):
        with st.container():
            st.divider()
            st.subheader("Your Coaching Feedback")
            st.markdown(st.session_state['last_feedback'])
            
            # Show success message if rating was just submitted
            if st.session_state.get('rating_submitted', False):
                st.success("✓ Feedback rated successfully")
                # Clear the flag after showing once
                st.session_state['rating_submitted'] = False
    
    # Clear the just_generated flag for next run
    if 'just_generated' in st.session_state:
        st.session_state['just_generated'] = False
    
    # Display rating UI if feedback was just generated
    if 'last_feedback' in st.session_state and st.session_state.get('show_rating', False):
        st.divider()
        st.subheader("Rate This Coaching")
        
        col1, col2 = st.columns([1, 2])
        
        with col1:
            rating = st.selectbox(
                "How good was this feedback?",
                options=[3, 5, 4, 2, 1],  # 3 first as default
                format_func=lambda x: {
                    5: "5 - Perfect",
                    4: "4 - Good", 
                    3: "3 - Okay",
                    2: "2 - Poor",
                    1: "1 - Awful"
                }[x],
                key="feedback_rating"
            )
        
        with col2:
            admin_feedback = st.text_area(
                "What would you change?",
                placeholder="e.g., 'Should have caught the weak hook earlier' or 'Missed the buried lede in paragraph 3' or 'Examples weren't relevant'",
                height=120,
                key="admin_feedback"
            )
        
        if st.button("Submit Rating", type="primary", key="submit_rating", use_container_width=True):
            # Get Supabase logger
            logger = get_supabase_logger()
            
            # Get session_id from session state if available
            session_id = st.session_state.get('current_session_id')
            
            if logger and session_id:
                try:
                    # Save feedback to Supabase
                    logger.save_feedback(
                        session_id=session_id,
                        rating=rating,
                        feedback_text=admin_feedback if admin_feedback else None
                    )
                    st.success(f"✓ Rating saved: {rating}/5")
                    if admin_feedback:
                        st.info(f"Notes recorded: {admin_feedback}")
                    # Hide rating UI but keep feedback visible
                    st.session_state['show_rating'] = False
                    st.session_state['rating_submitted'] = True
                    st.rerun()  # Force immediate rerun to show feedback
                except Exception as e:
                    st.error(f"Failed to save feedback: {e}")
            else:
                # Just show confirmation if no logger/session
                st.success(f"✓ Rating saved locally: {rating}/5")
                if admin_feedback:
                    st.info(f"Notes recorded: {admin_feedback}")
                # Hide rating UI but keep feedback visible
                st.session_state['show_rating'] = False
                st.session_state['rating_submitted'] = True
                st.rerun()  # Force immediate rerun to show feedback

if __name__ == "__main__":
    main()