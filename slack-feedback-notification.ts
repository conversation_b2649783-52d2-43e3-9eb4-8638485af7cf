import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'

const SLACK_WEBHOOK_URL = '*******************************************************************************'

serve(async (req) => {
  try {
    // Get the webhook payload from Supabase
    const payload = await req.json()
    const record = payload.record
    const table = payload.table
    
    // Your project ref (from the logs)
    const projectRef = 'isujxuylezybmndycthy'
    
    // Build direct link to this row in Supabase dashboard
    // Using the correct structure with table ID and schema
    const tableId = '17303'  // user_feedback table ID
    const dashboardLink = `https://supabase.com/dashboard/project/${projectRef}/editor/${tableId}?schema=public&filter=id%3Deq%3D${record.id}`
    
    // Format message for Slack
    const slackMessage = {
      text: `🔔 New Feedback from Adam`,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*New Feedback Received*\n*Rating:* ${record.rating}/5\n*Session:* ${record.session_id?.slice(0, 8)}...`
          }
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Feedback:*\n${record.feedback_text || 'No text provided'}`
          }
        },
        {
          type: 'actions',
          elements: [
            {
              type: 'button',
              text: {
                type: 'plain_text',
                text: '🔗 View in Supabase'
              },
              url: dashboardLink
            }
          ]
        }
      ]
    }
    
    // Send to Slack
    const slackResponse = await fetch(SLACK_WEBHOOK_URL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(slackMessage)
    })
    
    return new Response('OK', { status: 200 })
    
  } catch (error) {
    console.error('Error:', error)
    return new Response(JSON.stringify({ error: error.message }), { 
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
})