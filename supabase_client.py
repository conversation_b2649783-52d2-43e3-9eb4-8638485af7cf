"""Supabase client for logging API traces and feedback"""
import os
from typing import Dict, Any, Optional
import uuid
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()

class SupabaseLogger:
    def __init__(self):
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not url or not key:
            raise ValueError("Missing SUPABASE_URL or SUPABASE_SERVICE_KEY in .env")
        
        self.client: Client = create_client(url, key)
    
    def log_api_trace(
        self,
        session_id: str,
        user_draft: str,
        request_payload: Dict[str, Any],
        response_payload: Dict[str, Any],
        similar_posts: list,
        input_tokens: Optional[int] = None,
        output_tokens: Optional[int] = None,
        latency_ms: Optional[int] = None,
        model: str = "claude-sonnet-4-20250514"
    ) -> Dict[str, Any]:
        """Log an API interaction to Supabase"""
        
        data = {
            "session_id": session_id,
            "user_draft": user_draft,
            "request_payload": request_payload,
            "response_payload": response_payload,
            "model": model,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "latency_ms": latency_ms,
            "similar_posts_retrieved": similar_posts
        }
        
        response = self.client.table("api_traces").insert(data).execute()
        return response.data[0] if response.data else None
    
    def save_feedback(
        self,
        session_id: str,
        rating: int,
        feedback_text: Optional[str] = None
    ) -> Dict[str, Any]:
        """Save user feedback for a session"""
        
        if not 1 <= rating <= 5:
            raise ValueError("Rating must be between 1 and 5")
        
        data = {
            "session_id": session_id,
            "rating": rating,
            "feedback_text": feedback_text
        }
        
        response = self.client.table("user_feedback").insert(data).execute()
        return response.data[0] if response.data else None
    
    def get_session_trace(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve a specific session's trace"""
        response = self.client.table("api_traces").select("*").eq("session_id", session_id).execute()
        return response.data[0] if response.data else None
    
    def get_session_feedback(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve feedback for a specific session"""
        response = self.client.table("user_feedback").select("*").eq("session_id", session_id).execute()
        return response.data[0] if response.data else None

def generate_session_id() -> str:
    """Generate a unique session ID"""
    return str(uuid.uuid4())