# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
This is a LinkedIn coaching feedback bot that provides Adam Knorr-style feedback on LinkedIn posts by leveraging a database of existing coaching feedback. The system uses similarity search on embeddings to find relevant coaching examples and generates personalized feedback using Claude Sonnet 4.

## Architecture
The system follows this data flow:
1. **Data Import**: Notion coaching database → MongoDB Atlas with vector search
2. **Embedding Pipeline**: User draft → VoyageAI embeddings → similarity search
3. **Feedback Generation**: Retrieved examples → Claude Sonnet 4 → formatted feedback

## Key Components
- **import_coaching_data.py**: Imports coaching data from Notion exports (CSV/markdown) into MongoDB
- **generate_embeddings.py**: Generates vector embeddings for all content fields using Voyage AI
- **MongoDB Schema**: Stores coaching insights with fields for original_content, edited_content, coaching_feedback, client, post_type, content_strategy_tags, plus embeddings
- **Vector Embeddings**: Uses voyage-3-large model (1024 dimensions) for coaching_feedback, original_content, and edited_content fields

## Development Commands

### Python Environment
```bash
# Install dependencies (when requirements.txt is created)
pip install -r requirements.txt

# Run MongoDB connection test
python tests/test_mongodb_connection.py

# Import coaching data from Notion export
python import_coaching_data.py

# Generate embeddings for all documents in MongoDB
python generate_embeddings.py
```

### Environment Setup
Create a `.env` file with:
```
MONGODB_URI=<your_mongodb_atlas_connection_string>
ANTHROPIC_API_KEY=<your_claude_api_key>
VOYAGE_API_KEY=<your_voyage_ai_key>
```

## Current Implementation Status
According to the PRD, the project is in Phase 1-2 (Data Prep & Embeddings):
- ✅ MongoDB Atlas setup is complete
- ✅ Data import script exists and works
- ✅ Vector embeddings generated for all 20 documents (voyage-3-large, 1024 dims)
- ⏳ MongoDB vector search index configuration pending
- ⏳ Semantic search functionality not yet implemented
- ⏳ Streamlit UI not yet built
- ⏳ Claude Sonnet integration pending

## Tech Stack Context
- **Database**: MongoDB Atlas (with planned vector search capabilities)
- **LLM**: Claude Sonnet 4 (via Anthropic API)
- **Embeddings**: VoyageAI (planned)
- **Frontend**: Streamlit (planned)
- **Hosting**: Heroku

## Data Sources
The system expects coaching data in `/Users/<USER>/adam-feedback-bot/adam-notion-export/` containing:
- CSV export from Notion database
- Individual markdown files with coaching sessions

## Next Development Steps
Based on the PRD Phase 1-4 roadmap:
1. ~~Implement VoyageAI embedding generation~~ ✅ Complete
2. Configure MongoDB vector search index
3. Build semantic search functionality (find similar posts/coaching)
4. Update import script to auto-generate embeddings for new data
5. Integrate Claude Sonnet for feedback generation
6. Create Streamlit interface (Phase 3)